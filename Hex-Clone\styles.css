* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
    background-color: #0a0a0a;
    color: #ffffff;
    line-height: 1.6;
}

/* Top Banner */
.top-banner {
    background: linear-gradient(90deg, #1a1a1a 0%, #2a2a2a 50%, #1a1a1a 100%);
    padding: 8px 0;
    font-size: 13px;
    border-bottom: 1px solid #333;
    overflow: hidden;
}

.banner-content {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 16px;
    animation: scroll 30s linear infinite;
    white-space: nowrap;
}

.banner-icon {
    font-size: 14px;
}

.banner-divider {
    color: #666;
    margin: 0 8px;
}

@keyframes scroll {
    0% { transform: translateX(100%); }
    100% { transform: translateX(-100%); }
}

/* Navigation */
.navbar {
    background-color: #0a0a0a;
    padding: 16px 0;
    border-bottom: 1px solid #1a1a1a;
    position: sticky;
    top: 0;
    z-index: 100;
}

.nav-container {
    max-width: 1200px;
    margin: 0 auto;
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0 24px;
}

.nav-left {
    display: flex;
    align-items: center;
    gap: 48px;
}

.logo {
    font-weight: 700;
    font-size: 24px;
    color: #ff6b35;
}

.logo-text {
    background: linear-gradient(45deg, #ff6b35, #ff8c42);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.nav-links {
    display: flex;
    gap: 32px;
}

.nav-item {
    display: flex;
    align-items: center;
    gap: 4px;
    cursor: pointer;
    font-weight: 500;
    color: #e0e0e0;
    transition: color 0.2s;
}

.nav-item:hover {
    color: #ffffff;
}

.dropdown-arrow {
    font-size: 10px;
    color: #888;
}

.nav-right {
    display: flex;
    gap: 16px;
}

/* Buttons */
.btn-primary, .btn-secondary {
    padding: 12px 24px;
    border-radius: 8px;
    font-weight: 600;
    font-size: 14px;
    cursor: pointer;
    transition: all 0.2s;
    border: none;
    display: flex;
    align-items: center;
    gap: 8px;
}

.btn-primary {
    background: linear-gradient(135deg, #ff6b35, #ff8c42);
    color: white;
}

.btn-primary:hover {
    transform: translateY(-1px);
    box-shadow: 0 8px 25px rgba(255, 107, 53, 0.3);
}

.btn-secondary {
    background: transparent;
    color: #e0e0e0;
    border: 1px solid #333;
}

.btn-secondary:hover {
    background-color: #1a1a1a;
    border-color: #555;
}

.btn-primary.large, .btn-secondary.large {
    padding: 16px 32px;
    font-size: 16px;
}

.btn-icon {
    font-size: 16px;
}

/* Mobile Menu */
.mobile-menu-toggle {
    display: none;
    flex-direction: column;
    justify-content: space-around;
    width: 32px;
    height: 32px;
    background: transparent;
    border: none;
    cursor: pointer;
    padding: 4px;
    z-index: 1001;
    border-radius: 4px;
    transition: background-color 0.2s ease;
}

.mobile-menu-toggle:hover {
    background-color: rgba(255, 255, 255, 0.1);
}

.mobile-menu-toggle span {
    width: 100%;
    height: 3px;
    background: #ffffff;
    border-radius: 2px;
    transition: all 0.3s ease;
    display: block;
}

.mobile-menu-toggle.active span:nth-child(1) {
    transform: rotate(45deg) translate(8px, 8px);
}

.mobile-menu-toggle.active span:nth-child(2) {
    opacity: 0;
}

.mobile-menu-toggle.active span:nth-child(3) {
    transform: rotate(-45deg) translate(7px, -6px);
}

.mobile-menu {
    display: none;
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100vh;
    background: rgba(10, 10, 10, 0.95);
    backdrop-filter: blur(10px);
    z-index: 1000;
    opacity: 0;
    visibility: hidden;
    transition: all 0.3s ease;
}

.mobile-menu.active {
    opacity: 1;
    visibility: visible;
}

.mobile-menu-content {
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    height: 100%;
    gap: 30px;
    padding: 20px;
}

.mobile-nav-item {
    font-size: 24px;
    font-weight: 500;
    color: #ffffff;
    cursor: pointer;
    transition: color 0.2s ease;
    text-align: center;
}

.mobile-nav-item:hover {
    color: #ff6b35;
}

.mobile-menu-buttons {
    display: flex;
    flex-direction: column;
    gap: 16px;
    margin-top: 40px;
    width: 100%;
    max-width: 300px;
}

.mobile-menu-buttons .btn-primary,
.mobile-menu-buttons .btn-secondary {
    width: 100%;
    justify-content: center;
}

/* Main Content */
.main-content {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 24px;
}

.hero-section {
    display: grid;
    grid-template-columns: 1fr 1.2fr;
    gap: 80px;
    align-items: center;
    padding: 80px 0;
    min-height: 80vh;
}

.hero-left {
    max-width: 500px;
}

.hero-title {
    font-size: 64px;
    font-weight: 700;
    line-height: 1.1;
    margin-bottom: 24px;
    background: linear-gradient(135deg, #ffffff, #e0e0e0);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.hero-subtitle {
    font-size: 20px;
    color: #b0b0b0;
    margin-bottom: 40px;
    line-height: 1.5;
}

.hero-buttons {
    display: flex;
    gap: 16px;
}

/* Hero Video Container */
.hero-video-container {
    position: relative;
    background: #1a1a1a;
    border-radius: 16px;
    overflow: hidden;
    box-shadow: 0 20px 60px rgba(0, 0, 0, 0.5);
    border: 1px solid #2a2a2a;
    aspect-ratio: 16/9;
    max-width: 100%;
}

.hero-video-container video {
    width: 100%;
    height: 100%;
    object-fit: cover;
    border-radius: 16px;
}

.video-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(
        to bottom,
        rgba(0, 0, 0, 0) 0%,
        rgba(0, 0, 0, 0) 70%,
        rgba(0, 0, 0, 0.3) 100%
    );
    opacity: 0;
    transition: opacity 0.3s ease;
    pointer-events: none;
}

.hero-video-container:hover .video-overlay {
    opacity: 1;
}

.video-controls {
    position: absolute;
    bottom: 20px;
    right: 20px;
    display: flex;
    gap: 12px;
    pointer-events: all;
}

.video-play-btn,
.video-fullscreen-btn {
    background: rgba(0, 0, 0, 0.7);
    border: none;
    color: white;
    padding: 12px;
    border-radius: 8px;
    font-size: 16px;
    cursor: pointer;
    transition: all 0.2s ease;
    backdrop-filter: blur(10px);
}

.video-play-btn:hover,
.video-fullscreen-btn:hover {
    background: rgba(255, 107, 53, 0.8);
    transform: scale(1.05);
}

.video-placeholder {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    background: linear-gradient(135deg, #1a1a1a, #2a2a2a);
    color: #e0e0e0;
    z-index: 2;
}

.play-button {
    font-size: 48px;
    color: #ff6b35;
    margin-bottom: 16px;
    cursor: pointer;
    transition: transform 0.2s;
}

.play-button:hover {
    transform: scale(1.1);
}

/* Section Styles */
.section-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 24px;
}

.section-header {
    text-align: center;
    margin-bottom: 80px;
}

.section-title {
    font-size: 48px;
    font-weight: 700;
    color: #ffffff;
    margin-bottom: 24px;
    line-height: 1.1;
}

.section-subtitle {
    font-size: 18px;
    color: #888;
    max-width: 600px;
    margin: 0 auto;
    line-height: 1.6;
}

.section-badge {
    display: inline-block;
    background: rgba(255, 107, 53, 0.1);
    color: #ff6b35;
    padding: 8px 16px;
    border-radius: 20px;
    font-size: 12px;
    font-weight: 600;
    letter-spacing: 1px;
    margin-bottom: 24px;
}

/* One Home Section */
.one-home-section {
    padding: 120px 0;
    background: #0a0a0a;
}

.features-grid {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 40px;
    margin-bottom: 80px;
}

.feature-card {
    display: flex;
    flex-direction: column;
    align-items: center;
    text-align: center;
    padding: 20px;
}

.feature-card:hover {
    transform: translateY(-8px);
    border-color: #ff6b35;
    box-shadow: 0 20px 40px rgba(255, 107, 53, 0.1);
}

.feature-icon {
    width: 64px;
    height: 64px;
    margin: 0 auto 24px;
    background: #2a2a2a;
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.feature-icon img {
    width: 200px; /* Adjust this value to make the image bigger */
    height: auto;
    margin-bottom: 16px;
}

.feature-badge {
    display: inline-block;
    background: rgba(255, 107, 53, 0.1);
    color: #ff6b35;
    padding: 6px 12px;
    border-radius: 16px;
    font-size: 10px;
    font-weight: 600;
    letter-spacing: 1px;
    margin-bottom: 16px;
}

.feature-card .feature-badge {
    margin-top: 18px; /* Adds extra space between the image and the badge text */
}

.feature-title {
    font-size: 20px;
    font-weight: 600;
    color: #ffffff;
    margin-bottom: 16px;
}

.feature-description {
    font-size: 14px;
    color: #888;
    line-height: 1.6;
}

.platform-preview {
    background: #1a1a1a;
    border: 1px solid #2a2a2a;
    border-radius: 16px;
    padding: 32px;
    text-align: center;
}

.preview-tabs {
    display: flex;
    justify-content: center;
    gap: 32px;
    flex-wrap: wrap;
    padding: 0 20px;
}

.preview-tab {
    font-size: 12px;
    font-weight: 600;
    letter-spacing: 1px;
    color: #888;
    cursor: pointer;
    padding: 12px 8px;
    border-bottom: 2px solid transparent;
    transition: all 0.2s ease;
    text-align: center;
    white-space: nowrap;
}

.preview-tab.active {
    color: #ff6b35;
    border-bottom-color: #ff6b35;
}

.preview-tab:hover {
    color: #e0e0e0;
}

/* Analytics Section */
.analytics-section {
    padding: 120px 0;
    background: #0f0f0f;
    position: relative;
}

.analytics-header {
    text-align: center;
    max-width: 700px;
    margin: 0 auto 80px;
}

.section-badge {
    display: inline-block;
    background: linear-gradient(90deg, #ff6b35, #ff8c42);
    color: #000;
    font-size: 12px;
    font-weight: 600;
    padding: 6px 12px;
    border-radius: 4px;
    margin-bottom: 24px;
    letter-spacing: 1px;
}

.section-title {
    font-size: 48px;
    font-weight: 700;
    margin-bottom: 24px;
    background: linear-gradient(135deg, #ffffff, #e0e0e0);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.section-description {
    font-size: 18px;
    color: #b0b0b0;
    line-height: 1.6;
}

.analytics-content {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 80px;
    align-items: center;
}

.analytics-left {
    max-width: 500px;
}

.analytics-left .section-title {
    text-align: left;
    margin-bottom: 32px;
    font-size: 42px;
}

.section-description {
    font-size: 16px;
    color: #888;
    line-height: 1.6;
    margin-bottom: 48px;
}

.analytics-features {
    display: flex;
    flex-direction: column;
    gap: 32px;
}

.analytics-feature h4 {
    font-size: 16px;
    font-weight: 600;
    color: #ffffff;
    margin-bottom: 8px;
}

.analytics-feature p {
    font-size: 14px;
    color: #888;
    line-height: 1.5;
}

.analytics-right {
    position: relative;
}

.analytics-preview {
    background: #1a1a1a;
    border: 1px solid #2a2a2a;
    border-radius: 16px;
    overflow: hidden;
    box-shadow: 0 20px 60px rgba(0, 0, 0, 0.5);
}

.preview-header {
    background: #0f0f0f;
    padding: 20px 24px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    border-bottom: 1px solid #2a2a2a;
}

.preview-title {
    font-size: 16px;
    font-weight: 600;
    color: #ffffff;
}

.preview-controls {
    display: flex;
    gap: 12px;
}

.preview-btn {
    background: #2a2a2a;
    border: none;
    color: #e0e0e0;
    padding: 8px 16px;
    border-radius: 6px;
    font-size: 12px;
    cursor: pointer;
    transition: background 0.2s ease;
}

.preview-btn:hover {
    background: #3a3a3a;
}

.preview-content {
    padding: 24px;
    height: 300px;
}

.preview-chart {
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, #1a1a1a, #2a2a2a);
    border-radius: 8px;
    position: relative;
}

#analyticsChart {
    width: 100% !important;
    height: 100% !important;
}

/* Testimonials Section */
.testimonials-section {
    padding: 80px 0;
    background: #0a0a0a;
}

.testimonials-container {
    max-width: 100%;
    margin: 0 auto;
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 0;
    height: auto;
}

.testimonial-box {
    padding: 60px 40px;
    background: #1a1a1a;
    border: 1px solid #2a2a2a;
    display: flex;
    flex-direction: column;
    justify-content: center;
    min-height: 300px;
    transition: transform 0.3s ease, box-shadow 0.3s ease, border-color 0.3s ease;
}

.testimonial-box.left {
    border-right: none;
    border-radius: 16px 0 0 16px;
}

.testimonial-box.right {
    border-left: none;
    border-radius: 0 16px 16px 0;
}

.testimonial-box:hover {
    transform: translateY(-5px);
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
    border-color: #ff6b35;
    background: #1f1f1f;
}

.testimonial-logo {
    font-size: 24px;
    font-weight: 700;
    color: #ff6b35;
    margin-bottom: 24px;
    text-transform: uppercase;
    letter-spacing: 1px;
}

.testimonial-quote {
    font-size: 18px;
    line-height: 1.6;
    color: #e0e0e0;
    margin-bottom: 32px;
    font-style: italic;
    flex-grow: 1;
}

.testimonial-author {
    display: flex;
    flex-direction: column;
    gap: 8px;
}

.author-name {
    font-weight: 600;
    color: #ffffff;
    font-size: 16px;
}

.testimonial-role {
    font-size: 14px;
    color: #888;
    font-weight: 400;
}

/* Responsive design for testimonials */
@media (max-width: 768px) {
    .testimonials-container {
        grid-template-columns: 1fr;
        gap: 20px;
    }

    .testimonial-box.left,
    .testimonial-box.right {
        border-radius: 16px;
        border: 1px solid #2a2a2a;
    }

    .testimonial-box {
        padding: 40px 30px;
        min-height: 250px;
    }

    .testimonial-quote {
        font-size: 16px;
    }
}

/* Trusted By Section */
.trusted-section {
    padding: 80px 0;
    border-top: 1px solid #1a1a1a;
}

.trusted-content {
    text-align: center;
}

.trusted-text {
    font-size: 12px;
    color: #666;
    margin-bottom: 40px;
    letter-spacing: 2px;
    font-weight: 600;
}

.company-logos {
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 48px;
    flex-wrap: wrap;
}

.logo-item {
    font-size: 16px;
    font-weight: 600;
    color: #666;
    opacity: 0.7;
    transition: opacity 0.2s;
}

.logo-item:hover {
    opacity: 1;
}

/* Footer */
.footer {
    background: #0f0f0f;
    border-top: 1px solid #1a1a1a;
    padding: 60px 0 0;
    margin-top: 80px;
}

.footer-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 24px;
    display: grid;
    grid-template-columns: 1fr 2fr 1fr;
    gap: 60px;
    align-items: start;
}

.footer-left {
    display: flex;
    flex-direction: column;
    gap: 16px;
}

.footer-logo .logo-text {
    font-size: 24px;
    font-weight: 700;
    background: linear-gradient(45deg, #ff6b35, #ff8c42);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.footer-tagline {
    color: #888;
    font-size: 14px;
}

.footer-links {
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    gap: 40px;
}

.footer-column h4 {
    color: #e0e0e0;
    font-size: 12px;
    font-weight: 600;
    letter-spacing: 1px;
    margin-bottom: 20px;
}

.footer-column ul {
    list-style: none;
    display: flex;
    flex-direction: column;
    gap: 12px;
}

.footer-column a {
    color: #888;
    text-decoration: none;
    font-size: 14px;
    transition: color 0.2s;
}

.footer-column a:hover {
    color: #e0e0e0;
}

.footer-right {
    display: flex;
    justify-content: flex-end;
}

.social-icons {
    display: flex;
    gap: 16px;
}

.social-icons span {
    font-size: 20px;
    cursor: pointer;
    opacity: 0.7;
    transition: opacity 0.2s, transform 0.2s;
}

.social-icons span:hover {
    opacity: 1;
    transform: translateY(-2px);
}

.footer-bottom {
    border-top: 1px solid #1a1a1a;
    margin-top: 40px;
    padding: 24px 0;
}

.footer-bottom-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 24px;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.footer-bottom p {
    color: #666;
    font-size: 14px;
}

.footer-bottom-links {
    display: flex;
    gap: 24px;
}

.footer-bottom-links a {
    color: #666;
    text-decoration: none;
    font-size: 14px;
    transition: color 0.2s;
}

.footer-bottom-links a:hover {
    color: #888;
}

/* Feature Showcase Styles */
.feature-showcase {
    display: flex;
    flex-direction: column;
    gap: 80px;
    margin-top: 80px;
}

.feature-showcase-item {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 40px;
    align-items: center;
    padding: 30px;
    border-radius: 16px;
    background: rgba(26, 26, 26, 0.5);
    border: 1px solid #2a2a2a;
    transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.feature-showcase-item:hover {
    transform: translateY(-5px);
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
    border-color: #ff6b35;
}

/* Content left, Image right (default and for content-left class) */
.feature-showcase-item.content-left {
    grid-template-columns: 1fr 1fr;
}

.feature-showcase-item.content-left .feature-content {
    order: 1;
}

.feature-showcase-item.content-left .feature-image {
    order: 2;
}

/* Image left, Content right (for image-left class) */
.feature-showcase-item.image-left {
    grid-template-columns: 1fr 1fr;
}

.feature-showcase-item.image-left .feature-image {
    order: 1;
}

.feature-showcase-item.image-left .feature-content {
    order: 2;
}

.feature-content {
    display: flex;
    flex-direction: column;
    gap: 16px;
}

.feature-icon {
    width: 60px;
    height: 60px;
    background: #2a2a2a;
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 24px;
    margin-bottom: 8px;
}

.feature-showcase-item h3 {
    font-size: 24px;
    font-weight: 600;
    color: #ffffff;
    margin-bottom: 8px;
}

.feature-showcase-item p {
    font-size: 16px;
    color: #b0b0b0;
    line-height: 1.5;
}

.feature-image {
    overflow: hidden;
    border-radius: 12px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
    transition: transform 0.3s ease, box-shadow 0.3s ease;
    background: #1a1a1a;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
}

.feature-image img {
    width: 100%;
    height: auto;
    object-fit: cover;
    transition: transform 0.3s ease;
}

.feature-showcase-item:hover .feature-image {
    box-shadow: 0 15px 40px rgba(255, 107, 53, 0.2);
}

.feature-showcase-item:hover .feature-image img {
    transform: scale(1.05);
}

/* ===== RESPONSIVE DESIGN ===== */

/* Large Desktop (1200px and up) */
@media (min-width: 1200px) {
    .section-container {
        max-width: 1400px;
    }

    .hero-title {
        font-size: 72px;
    }

    .section-title {
        font-size: 56px;
    }
}

/* Desktop (1024px to 1199px) */
@media (max-width: 1199px) {
    .section-container {
        max-width: 1000px;
    }

    .hero-section {
        gap: 60px;
    }

    .features-grid {
        gap: 30px;
    }
}

/* Tablet (768px to 1023px) */
@media (max-width: 1023px) {
    .nav-container {
        padding: 0 20px;
    }

    .main-content {
        padding: 0 20px;
    }

    .section-container {
        padding: 0 20px;
    }

    .hero-section {
        grid-template-columns: 1fr;
        gap: 40px;
        text-align: center;
        padding: 60px 0;
    }

    .hero-title {
        font-size: 48px;
    }

    .hero-subtitle {
        font-size: 18px;
    }

    .features-grid {
        grid-template-columns: repeat(2, 1fr);
        gap: 30px;
    }

    .footer-container {
        grid-template-columns: 1fr;
        gap: 40px;
        text-align: center;
    }

    .footer-links {
        grid-template-columns: repeat(2, 1fr);
        gap: 30px;
    }

    /* Preview tabs for tablets */
    .preview-tabs {
        gap: 24px;
        padding: 0 16px;
    }

    .preview-tab {
        font-size: 11px;
        padding: 12px 8px;
    }
}

/* Mobile Large (481px to 767px) */
@media (max-width: 767px) {
    .navbar {
        padding: 12px 0;
    }

    .nav-container {
        padding: 0 16px;
    }

    .nav-left {
        gap: 20px;
    }

    .logo {
        font-size: 20px;
    }

    .nav-links {
        display: none;
    }

    .nav-right {
        gap: 8px;
        position: relative;
        align-items: center;
    }

    .nav-right .btn-secondary,
    .nav-right .btn-primary {
        display: none;
    }

    .mobile-menu-toggle {
        display: flex;
        position: relative;
        z-index: 1002;
    }

    .mobile-menu {
        display: block;
    }

    .btn-primary, .btn-secondary {
        padding: 10px 16px;
        font-size: 12px;
    }

    .hero-title {
        font-size: 36px;
        line-height: 1.2;
    }

    .hero-subtitle {
        font-size: 16px;
        margin-bottom: 30px;
    }

    .hero-buttons {
        flex-direction: column;
        gap: 12px;
    }

    .section-title {
        font-size: 32px;
    }

    .section-subtitle {
        font-size: 16px;
    }

    .features-grid {
        grid-template-columns: 1fr;
        gap: 30px;
    }

    .feature-showcase-item,
    .feature-showcase-item.content-left,
    .feature-showcase-item.image-left {
        grid-template-columns: 1fr;
        gap: 30px;
        padding: 20px;
    }

    /* Reset order for mobile - always show content first, then image */
    .feature-showcase-item.content-left .feature-content,
    .feature-showcase-item.image-left .feature-content {
        order: 1;
    }

    .feature-showcase-item.content-left .feature-image,
    .feature-showcase-item.image-left .feature-image {
        order: 2;
    }

    .analytics-header {
        text-align: center;
        padding: 0 20px;
    }

    .one-home-section,
    .analytics-section {
        padding: 80px 0;
    }

    .section-header {
        margin-bottom: 60px;
    }

    .feature-showcase {
        gap: 60px;
    }

    .footer-links {
        grid-template-columns: 1fr;
        gap: 30px;
    }

    .footer-bottom-container {
        flex-direction: column;
        gap: 20px;
        text-align: center;
    }

    .company-logos {
        gap: 24px;
    }

    .logo-item {
        font-size: 14px;
    }

    /* Fix preview tabs on mobile */
    .preview-tabs {
        gap: 16px;
        padding: 0 10px;
        justify-content: center;
        flex-wrap: wrap;
    }

    .preview-tab {
        font-size: 10px;
        padding: 12px 6px;
        letter-spacing: 0.5px;
        min-width: auto;
        flex: 0 0 auto;
    }

    .platform-preview {
        padding: 20px 16px;
    }
}

/* Mobile Small (320px to 480px) */
@media (max-width: 480px) {
    .nav-container {
        padding: 0 16px;
    }

    .main-content {
        padding: 0 16px;
    }

    .section-container {
        padding: 0 16px;
    }

    .hero-title {
        font-size: 28px;
        line-height: 1.3;
    }

    .hero-subtitle {
        font-size: 14px;
    }

    .btn-primary.large, .btn-secondary.large {
        padding: 12px 20px;
        font-size: 14px;
    }

    .section-title {
        font-size: 28px;
        line-height: 1.2;
    }

    .section-subtitle {
        font-size: 14px;
    }

    .feature-card {
        padding: 16px;
    }

    .feature-title {
        font-size: 18px;
    }

    .feature-description {
        font-size: 13px;
    }

    .feature-showcase-item h3 {
        font-size: 20px;
    }

    .feature-showcase-item p {
        font-size: 14px;
    }

    .testimonial-box {
        padding: 30px 20px;
        min-height: 200px;
    }

    .testimonial-quote {
        font-size: 14px;
    }

    .testimonial-logo {
        font-size: 20px;
    }

    .page-container {
        padding: 10px;
    }

    .one-home-section,
    .analytics-section,
    .testimonials-section {
        padding: 60px 0;
    }

    .trusted-section {
        padding: 60px 0;
    }

    .footer {
        padding: 40px 0 0;
        margin-top: 60px;
    }

    .footer-container {
        padding: 0 16px;
    }

    .company-logos {
        gap: 16px;
        flex-wrap: wrap;
    }

    .logo-item {
        font-size: 12px;
    }

    /* Extra small mobile fixes for preview tabs */
    .preview-tabs {
        gap: 8px;
        padding: 0 8px;
        flex-direction: column;
        align-items: center;
    }

    .preview-tab {
        font-size: 9px;
        padding: 10px 4px;
        letter-spacing: 0.3px;
        border-bottom: 1px solid transparent;
        width: 100%;
        max-width: 120px;
        text-align: center;
    }

    .preview-tab.active {
        border-bottom-color: #ff6b35;
    }

    .platform-preview {
        padding: 16px 12px;
    }
}

/* Extra responsive utilities */
@media (max-width: 767px) {
    /* Hide complex animations on mobile for better performance */
    .feature-showcase-item:hover {
        transform: none;
    }

    .feature-card:hover {
        transform: none;
    }

    /* Improve touch targets */
    .btn-primary, .btn-secondary {
        min-height: 44px;
        display: flex;
        align-items: center;
        justify-content: center;
    }

    /* Better spacing for mobile */
    .feature-showcase {
        gap: 40px;
        margin-top: 60px;
    }

    .section-header {
        margin-bottom: 40px;
    }

    /* Improve readability */
    .section-description {
        font-size: 15px;
        line-height: 1.7;
    }

    /* Better video container on mobile */
    .hero-video-container {
        margin-top: 20px;
    }

    /* Improve banner on mobile */
    .top-banner {
        font-size: 12px;
        padding: 6px 0;
    }

    .banner-content {
        gap: 12px;
    }
}

.page-container {
    position: relative;
    border: 2px solid #fff;
    width: 100%;
    min-height: 100vh; /* Ensure it fills at least the viewport height */
    margin: 0; /* Remove margins so it fills the space */
    padding: 20px;
    background-color: #0a0a0a; /* Base background */
    overflow: hidden;
    box-sizing: border-box;
}

.page-container::before {
    content: "";
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-image: 
        linear-gradient(90deg, rgba(255,255,255,0.15) 1px, transparent 1px),
        linear-gradient(180deg, rgba(255,255,255,0.15) 1px, transparent 1px);
    background-size: 50px 50px;
    pointer-events: none;
    z-index: 0;
}

/* Ensure inner content appears over the pattern */
.page-container > * {
    position: relative;
    z-index: 1;
}



